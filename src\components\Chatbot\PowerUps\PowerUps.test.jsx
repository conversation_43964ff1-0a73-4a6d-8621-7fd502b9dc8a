import { describe, expect, it, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import PowerUps from './index';
import * as chatbotService from '@/services/chatbot.service';
import useModalStore from '@/stores/modal/modalStore';
import useToast from '@/hooks/useToast';

// Mock dependencies
vi.mock('@/services/chatbot.service', () => ({
  getDNSRecords: vi.fn(),
  activateNewDesign: vi.fn(),
}));

vi.mock('@/stores/modal/modalStore', () => ({
  default: vi.fn(() => ({
    openPlansModal: vi.fn(),
  })),
}));

vi.mock('@/hooks/useToast', () => ({
  default: vi.fn(() => ({
    addErrorToast: vi.fn(),
    addWarningToast: vi.fn(),
  })),
}));

vi.mock('@/stores/chatbot/chatbotStore', () => ({
  useChatbotStore: vi.fn(() => ({
    selectedChatbot: { id: 'test-id' },
    setSelectedChatbot: vi.fn(),
  })),
}));

vi.mock('@/stores/teamManagement/teamManagementStore', () => ({
  default: vi.fn(() => ({
    selectedTeam: null,
  })),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: 'test-chatbot-id' }),
  };
});

// Mock other dependencies
vi.mock('@/helpers/tier/featureCheck', () => ({
  default: vi.fn(() => true),
}));

vi.mock('@/services/user.service', () => ({}));

const mockProps = {
  customizationData: {
    custom_url: 'test.example.com',
    custom_url_enabled: true,
  },
  updateCustomizationData: vi.fn(),
  errorLeadGenFields: {},
  setErrorLeadGenFields: vi.fn(),
  onCustomUrlErrorChange: vi.fn(),
  dropdownValidationErrors: {},
};

const renderWithRouter = (component) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('PowerUps DNS Records Error Handling', () => {
  let mockOpenPlansModal;
  let mockAddErrorToast;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockOpenPlansModal = vi.fn();
    mockAddErrorToast = vi.fn();
    
    useModalStore.mockReturnValue({
      openPlansModal: mockOpenPlansModal,
    });
    
    useToast.mockReturnValue({
      addErrorToast: mockAddErrorToast,
      addWarningToast: vi.fn(),
    });
  });

  it('should show upgrade popup when DNS records limit is reached', async () => {
    // Mock DNS records API to return a limit error
    const limitError = {
      response: {
        data: {
          detail: 'DNS records limit exceeded. Please upgrade your plan.',
        },
        status: 400,
      },
    };
    
    chatbotService.getDNSRecords.mockRejectedValue(limitError);

    renderWithRouter(<PowerUps {...mockProps} />);

    // Find and click the "Get DNS records" button
    const getDnsButton = screen.getByText('Get DNS records');
    fireEvent.click(getDnsButton);

    // Wait for the error handling to complete
    await waitFor(() => {
      expect(mockOpenPlansModal).toHaveBeenCalledWith('custom_url');
      expect(mockAddErrorToast).not.toHaveBeenCalled();
    });
  });

  it('should show upgrade popup for 402 status code (payment required)', async () => {
    const paymentRequiredError = {
      response: {
        data: {
          detail: 'Feature not available',
        },
        status: 402,
      },
    };
    
    chatbotService.getDNSRecords.mockRejectedValue(paymentRequiredError);

    renderWithRouter(<PowerUps {...mockProps} />);

    const getDnsButton = screen.getByText('Get DNS records');
    fireEvent.click(getDnsButton);

    await waitFor(() => {
      expect(mockOpenPlansModal).toHaveBeenCalledWith('custom_url');
      expect(mockAddErrorToast).not.toHaveBeenCalled();
    });
  });

  it('should show upgrade popup for custom URL limit errors', async () => {
    const customUrlLimitError = {
      response: {
        data: {
          detail: 'Custom URL limit reached. Maximum 5 custom URLs allowed.',
        },
        status: 400,
      },
    };
    
    chatbotService.getDNSRecords.mockRejectedValue(customUrlLimitError);

    renderWithRouter(<PowerUps {...mockProps} />);

    const getDnsButton = screen.getByText('Get DNS records');
    fireEvent.click(getDnsButton);

    await waitFor(() => {
      expect(mockOpenPlansModal).toHaveBeenCalledWith('custom_url');
      expect(mockAddErrorToast).not.toHaveBeenCalled();
    });
  });

  it('should show toast error for non-limit related errors', async () => {
    const generalError = {
      response: {
        data: {
          detail: 'Invalid domain format',
        },
        status: 400,
      },
    };
    
    chatbotService.getDNSRecords.mockRejectedValue(generalError);

    renderWithRouter(<PowerUps {...mockProps} />);

    const getDnsButton = screen.getByText('Get DNS records');
    fireEvent.click(getDnsButton);

    await waitFor(() => {
      expect(mockOpenPlansModal).not.toHaveBeenCalled();
      expect(mockAddErrorToast).toHaveBeenCalledWith({
        message: 'Invalid domain format',
      });
    });
  });

  it('should successfully handle DNS records retrieval', async () => {
    const successResponse = {
      status: 200,
      data: {
        dns_values: [
          { type: 'CNAME', name: 'test.example.com', value: 'dante-ai.com' },
        ],
      },
    };
    
    chatbotService.getDNSRecords.mockResolvedValue(successResponse);

    renderWithRouter(<PowerUps {...mockProps} />);

    const getDnsButton = screen.getByText('Get DNS records');
    fireEvent.click(getDnsButton);

    await waitFor(() => {
      expect(mockOpenPlansModal).not.toHaveBeenCalled();
      expect(mockAddErrorToast).not.toHaveBeenCalled();
      expect(mockProps.updateCustomizationData).toHaveBeenCalledWith(
        'dns_values',
        successResponse.data.dns_values
      );
    });
  });
});
